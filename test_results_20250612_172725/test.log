[2025-06-12 17:27:25] [INFO] 🚀 开始多线程API测试
[2025-06-12 17:27:25] [INFO] 📊 测试参数:
[2025-06-12 17:27:25] [INFO]    - API地址: http://localhost:8080
[2025-06-12 17:27:25] [INFO]    - 图片范围: 001.jpg - 003.jpg
[2025-06-12 17:27:25] [INFO]    - 总图片数: 3
[2025-06-12 17:27:25] [INFO]    - 线程数: 2
[2025-06-12 17:27:25] [INFO]    - 超时时间: 10s
[2025-06-12 17:27:25] [INFO]    - 结果目录: test_results_20250612_172725
[2025-06-12 17:27:25] [INFO] 线程1: 处理图片 001-002 (共2张)
[2025-06-12 17:27:25] [INFO] 线程2: 处理图片 003-003 (共1张)
[2025-06-12 17:27:25] [INFO] [线程1] 开始处理 2 张图片: 1-2
[2025-06-12 17:27:25] [INFO] [线程2] 开始处理 1 张图片: 3-3
[2025-06-12 17:27:25] [INFO] [线程1] 开始处理图片 001.jpg
[2025-06-12 17:27:25] [INFO] [线程2] 开始处理图片 003.jpg
[2025-06-12 17:27:28] [SUCCESS] [线程2] ✅ 003.jpg 处理成功 (2.59s)
[2025-06-12 17:27:28] [INFO] [线程2] 进度: 1/1 (100.0%)
[2025-06-12 17:27:28] [INFO] [线程2] 完成所有任务
[2025-06-12 17:27:28] [SUCCESS] 线程2 完成任务
[2025-06-12 17:27:28] [SUCCESS] [线程1] ✅ 001.jpg 处理成功 (2.63s)
[2025-06-12 17:27:28] [INFO] [线程1] 进度: 1/2 (50.0%)
[2025-06-12 17:27:28] [INFO] [线程1] 开始处理图片 002.jpg
[2025-06-12 17:27:30] [SUCCESS] [线程1] ✅ 002.jpg 处理成功 (2.31s)
[2025-06-12 17:27:30] [INFO] [线程1] 进度: 2/2 (100.0%)
[2025-06-12 17:27:30] [INFO] [线程1] 完成所有任务
[2025-06-12 17:27:30] [SUCCESS] 线程1 完成任务
[2025-06-12 17:27:30] [INFO] 📊 生成测试报告...
[2025-06-12 17:27:30] [SUCCESS] ✅ 测试完成！
