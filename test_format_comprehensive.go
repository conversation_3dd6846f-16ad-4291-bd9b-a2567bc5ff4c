package main

import (
	"fmt"
	"go-api-solve/internal/utils"
)

func testFormat(name, qwenResponse string) {
	fmt.Printf("=== 测试 %s ===\n", name)
	fmt.Println("输入数据:")
	fmt.Println(qwenResponse)
	fmt.Println()

	qwenData, err := utils.FormatQwenData(qwenResponse)
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		return
	}

	fmt.Println("格式化后的选项:")
	for key, value := range qwenData.Options {
		fmt.Printf("  %s: %s\n", key, value)
	}
	fmt.Println()
}

func main() {
	// 测试1: 分号分隔格式（id=42的情况）
	test1 := `{
		"question_type": "判断题",
		"question_text": "测试题目1",
		"options": {
			"Y;正确": "",
			"N;错误": ""
		}
	}`

	// 测试2: 冒号分隔格式
	test2 := `{
		"question_type": "判断题",
		"question_text": "测试题目2",
		"options": {
			"Y:正确": "",
			"N:错误": ""
		}
	}`

	// 测试3: 标准格式
	test3 := `{
		"question_type": "判断题",
		"question_text": "测试题目3",
		"options": {
			"Y": "正确",
			"N": "错误"
		}
	}`

	// 测试4: 多选题格式（确保不影响）
	test4 := `{
		"question_type": "多选题",
		"question_text": "测试题目4",
		"options": {
			"A": "选项A",
			"B": "选项B",
			"C": "选项C",
			"D": "选项D"
		}
	}`

	// 测试5: 混合格式
	test5 := `{
		"question_type": "判断题",
		"question_text": "测试题目5",
		"options": {
			"Y:正确": "覆盖值",
			"N;错误": ""
		}
	}`

	testFormat("分号分隔格式", test1)
	testFormat("冒号分隔格式", test2)
	testFormat("标准格式", test3)
	testFormat("多选题格式", test4)
	testFormat("混合格式", test5)
}
