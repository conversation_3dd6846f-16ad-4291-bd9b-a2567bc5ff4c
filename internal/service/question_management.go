package service

import (
	"context"
	"fmt"
	"go-api-solve/internal/model"
	"go-api-solve/internal/repository"
	"go-api-solve/internal/utils"
)

// QuestionManagementService 题库管理服务
type QuestionManagementService struct {
	questionRepo *repository.QuestionRepository
}

// NewQuestionManagementService 创建新的题库管理服务
func NewQuestionManagementService(questionRepo *repository.QuestionRepository) *QuestionManagementService {
	return &QuestionManagementService{
		questionRepo: questionRepo,
	}
}

// QuestionQueryParams 题库查询参数
type QuestionQueryParams struct {
	Page         int    `form:"page" binding:"omitempty,min=1"`
	PageSize     int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	QuestionType string `form:"question_type"`
	IsVerified   *int   `form:"is_verified" binding:"omitempty,oneof=0 1"`
	Keyword      string `form:"keyword"` // 搜索关键词，用于搜索题目内容
	StartDate    string `form:"start_date"` // 格式: 2006-01-02
	EndDate      string `form:"end_date"`   // 格式: 2006-01-02
	Sort         string `form:"sort" binding:"omitempty,oneof=id created_at updated_at question_type"`
	Order        string `form:"order" binding:"omitempty,oneof=asc desc"`
}

// GetDefaultQueryParams 获取默认查询参数
func (q *QuestionQueryParams) GetDefaultQueryParams() {
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 50 // 默认分页大小为50
	}
	if q.Sort == "" {
		q.Sort = "created_at"
	}
	if q.Order == "" {
		q.Order = "desc"
	}
}

// 实现QuestionQueryInterface接口
func (q *QuestionQueryParams) GetPage() int { return q.Page }
func (q *QuestionQueryParams) GetPageSize() int { return q.PageSize }
func (q *QuestionQueryParams) GetQuestionType() string { return q.QuestionType }
func (q *QuestionQueryParams) GetIsVerified() *int { return q.IsVerified }
func (q *QuestionQueryParams) GetKeyword() string { return q.Keyword }
func (q *QuestionQueryParams) GetStartDate() string { return q.StartDate }
func (q *QuestionQueryParams) GetEndDate() string { return q.EndDate }
func (q *QuestionQueryParams) GetSort() string { return q.Sort }
func (q *QuestionQueryParams) GetOrder() string { return q.Order }

// QuestionCreateRequest 创建题目请求
type QuestionCreateRequest struct {
	CacheKeyHash string                 `json:"cache_key_hash" binding:"required"`
	QuestionType string                 `json:"question_type" binding:"required,oneof=单选题 多选题 判断题"`
	QuestionText string                 `json:"question_text" binding:"required"`
	OptionA      *string                `json:"option_a,omitempty"`
	OptionB      *string                `json:"option_b,omitempty"`
	OptionC      *string                `json:"option_c,omitempty"`
	OptionD      *string                `json:"option_d,omitempty"`
	OptionY      *string                `json:"option_y,omitempty"`
	OptionN      *string                `json:"option_n,omitempty"`
	Answer       map[string]interface{} `json:"answer,omitempty"`
	Analysis     *string                `json:"analysis,omitempty"`
	UserImage    *string                `json:"user_image,omitempty"`
	ImageURL     *string                `json:"image_url,omitempty"`
	IsVerified   int                    `json:"is_verified" binding:"omitempty,oneof=0 1"`
}

// QuestionUpdateRequest 更新题目请求（限制字段）
type QuestionUpdateRequest struct {
	// 注意：id、qwen_raw、deepseek_raw、qwen_parsed 不允许修改
	CacheKeyHash *string                `json:"cache_key_hash,omitempty"`
	QuestionType *string                `json:"question_type,omitempty" binding:"omitempty,oneof=单选题 多选题 判断题"`
	QuestionText *string                `json:"question_text,omitempty"`
	OptionA      *string                `json:"option_a,omitempty"`
	OptionB      *string                `json:"option_b,omitempty"`
	OptionC      *string                `json:"option_c,omitempty"`
	OptionD      *string                `json:"option_d,omitempty"`
	OptionY      *string                `json:"option_y,omitempty"`
	OptionN      *string                `json:"option_n,omitempty"`
	Answer       map[string]interface{} `json:"answer,omitempty"`
	Analysis     *string                `json:"analysis,omitempty"`
	UserImage    *string                `json:"user_image,omitempty"`
	ImageURL     *string                `json:"image_url,omitempty"`
	IsVerified   *int                   `json:"is_verified,omitempty" binding:"omitempty,oneof=0 1"`
}

// ListQuestions 分页查询题库
func (s *QuestionManagementService) ListQuestions(ctx context.Context, params *QuestionQueryParams) (*utils.PaginationResult, error) {
	// 设置默认值
	params.GetDefaultQueryParams()
	
	// 查询数据
	questions, total, err := s.questionRepo.ListWithFilters(params)
	if err != nil {
		return nil, fmt.Errorf("failed to list questions: %w", err)
	}
	
	// 转换为响应格式
	var responses []*model.QuestionResponse
	for _, question := range questions {
		responses = append(responses, s.convertToResponse(question))
	}
	
	// 创建分页参数
	paginationParams := utils.NewPaginationParams(params.Page, params.PageSize, params.Sort, params.Order)
	
	// 返回分页结果
	return utils.NewPaginationResult(responses, paginationParams, total), nil
}

// GetQuestion 获取单个题目
func (s *QuestionManagementService) GetQuestion(ctx context.Context, id int64) (*model.QuestionResponse, error) {
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get question: %w", err)
	}
	
	return s.convertToResponse(question), nil
}

// CreateQuestion 创建题目
func (s *QuestionManagementService) CreateQuestion(ctx context.Context, req *QuestionCreateRequest) (*model.QuestionResponse, error) {
	// 转换为数据库模型
	question := &model.Question{
		CacheKeyHash: req.CacheKeyHash,
		QuestionType: req.QuestionType,
		QuestionText: req.QuestionText,
		OptionA:      req.OptionA,
		OptionB:      req.OptionB,
		OptionC:      req.OptionC,
		OptionD:      req.OptionD,
		OptionY:      req.OptionY,
		OptionN:      req.OptionN,
		Answer:       model.JSONField(req.Answer),
		Analysis:     req.Analysis,
		UserImage:    req.UserImage,
		ImageURL:     req.ImageURL,
		IsVerified:   req.IsVerified,
	}
	
	// 保存到数据库
	if err := s.questionRepo.Create(question); err != nil {
		return nil, fmt.Errorf("failed to create question: %w", err)
	}
	
	// 转换为响应格式
	return s.convertToResponse(question), nil
}

// UpdateQuestion 更新题目（受限字段）
func (s *QuestionManagementService) UpdateQuestion(ctx context.Context, id int64, req *QuestionUpdateRequest) (*model.QuestionResponse, error) {
	// 构建更新字段映射
	updates := make(map[string]interface{})
	
	if req.CacheKeyHash != nil {
		updates["cache_key_hash"] = *req.CacheKeyHash
	}
	if req.QuestionType != nil {
		updates["question_type"] = *req.QuestionType
	}
	if req.QuestionText != nil {
		updates["question_text"] = *req.QuestionText
	}
	if req.OptionA != nil {
		updates["option_a"] = *req.OptionA
	}
	if req.OptionB != nil {
		updates["option_b"] = *req.OptionB
	}
	if req.OptionC != nil {
		updates["option_c"] = *req.OptionC
	}
	if req.OptionD != nil {
		updates["option_d"] = *req.OptionD
	}
	if req.OptionY != nil {
		updates["option_y"] = *req.OptionY
	}
	if req.OptionN != nil {
		updates["option_n"] = *req.OptionN
	}
	if req.Answer != nil {
		updates["answer"] = model.JSONField(req.Answer)
	}
	if req.Analysis != nil {
		updates["analysis"] = *req.Analysis
	}
	if req.UserImage != nil {
		updates["user_image"] = *req.UserImage
	}
	if req.ImageURL != nil {
		updates["image_url"] = *req.ImageURL
	}
	if req.IsVerified != nil {
		updates["is_verified"] = *req.IsVerified
	}
	
	// 检查是否有字段需要更新
	if len(updates) == 0 {
		return nil, fmt.Errorf("no fields to update")
	}
	
	// 更新数据库
	if err := s.questionRepo.Update(id, updates); err != nil {
		return nil, fmt.Errorf("failed to update question: %w", err)
	}
	
	// 获取更新后的数据
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated question: %w", err)
	}
	
	return s.convertToResponse(question), nil
}

// DeleteQuestion 删除题目（物理删除）
func (s *QuestionManagementService) DeleteQuestion(ctx context.Context, id int64) error {
	if err := s.questionRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete question: %w", err)
	}
	
	return nil
}

// convertToResponse 转换为响应格式
func (s *QuestionManagementService) convertToResponse(question *model.Question) *model.QuestionResponse {
	// 构建选项映射
	options := make(map[string]string)
	if question.OptionA != nil {
		options["A"] = *question.OptionA
	}
	if question.OptionB != nil {
		options["B"] = *question.OptionB
	}
	if question.OptionC != nil {
		options["C"] = *question.OptionC
	}
	if question.OptionD != nil {
		options["D"] = *question.OptionD
	}
	if question.OptionY != nil {
		options["Y"] = *question.OptionY
	}
	if question.OptionN != nil {
		options["N"] = *question.OptionN
	}
	
	// 构建答案映射
	answer := make(map[string]string)
	if question.Answer != nil {
		for k, v := range question.Answer {
			if str, ok := v.(string); ok {
				answer[k] = str
			}
		}
	}
	
	// 处理分析内容
	analysis := ""
	if question.Analysis != nil {
		analysis = *question.Analysis
	}
	
	// 处理图片URL
	imageURL := ""
	if question.ImageURL != nil {
		imageURL = *question.ImageURL
	}
	
	// 处理验证状态
	isVerified := "0"
	if question.IsVerified == 1 {
		isVerified = "1"
	}
	
	return &model.QuestionResponse{
		ID:           question.ID,
		CacheKeyHash: question.CacheKeyHash,
		QuestionType: question.QuestionType,
		QuestionText: question.QuestionText,
		Options:      options,
		Answer:       answer,
		Analysis:     analysis,
		UserImage:    getUserImageValue(question.UserImage),
		ImageURL:     imageURL,
		IsVerified:   isVerified,
	}
}

// getUserImageValue 获取用户图片值，处理指针类型
func getUserImageValue(userImage *string) string {
	if userImage != nil {
		return *userImage
	}
	return ""
}
